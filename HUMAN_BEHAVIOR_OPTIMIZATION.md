# 真人行为模拟优化 - 提升成功率

## 问题分析
- 当前成功率：40%
- 主要问题：在step 12（验证完成后）容易被反欺诈系统检测为机器人
- 原因：操作过于机械化，缺少真人的自然行为

## 优化方案

### 新增真人行为模拟方法

#### 1. `simulateHumanPageExploration()`
- **功能**：模拟真人浏览页面的行为
- **行为**：
  - 随机滚动页面（1-3次，100-400px）
  - 有30%概率会回滚一部分（模拟回看）
  - 调用随机鼠标移动
- **用途**：在关键节点模拟查看页面内容

#### 2. `simulateRandomMouseMovement()`
- **功能**：模拟无意识的鼠标移动
- **行为**：
  - 1-3次随机移动
  - 移动轨迹有5-15步（模拟真实鼠标轨迹）
  - 移动范围在屏幕的80%区域内
- **用途**：增加鼠标行为的自然性

#### 3. `simulateHesitation()`
- **功能**：模拟真人的思考犹豫时间
- **行为**：
  - 800-2000ms的随机停顿
  - 40%概率会有小范围鼠标移动（模拟不确定手势）
  - 移动范围±50px，3步完成
- **用途**：在决策点增加真实的思考时间

#### 4. `simulateFocusChange()`
- **功能**：模拟焦点切换行为
- **行为**：
  - 30%概率执行
  - 按Tab键切换焦点
  - 50%概率会按两次Tab
- **用途**：模拟真人的页面导航习惯

#### 5. `simulateVerificationSuccessReaction()`
- **功能**：模拟验证成功后的自然反应
- **行为**：
  - 1-2秒停顿（确认成功）
  - 页面探索行为
  - 犹豫思考行为
- **用途**：在关键的step 12前增加真实性

## 关键优化点

### 1. 邮箱输入后 (Step 7)
```javascript
// 邮箱输入后的真人行为
await this.simulateHesitation();
await this.simulateFocusChange();
```

### 2. 验证码输入后 (Step 10)
```javascript
// 验证码输入后的真人行为
await this.simulateHesitation();
await this.simulateRandomMouseMovement();
```

### 3. 验证完成后 (Step 12前) - **最关键**
```javascript
// 模拟验证成功后的自然反应（关键优化点）
await this.simulateVerificationSuccessReaction();
```

### 4. 寻找Copy按钮前 (Step 12中)
```javascript
// 在寻找copy按钮前模拟真人行为（关键优化点）
this.log('👀 模拟查看页面内容...');
await this.simulateHumanPageExploration();
await this.simulateHesitation();
```

### 5. 点击Copy按钮前 (Step 12中)
```javascript
// 点击前的最后确认行为（关键优化点）
this.log('🎯 确认copy按钮位置...');
await this.simulateHesitation();
await this.simulateFocusChange();
```

## 预期效果

### 成功率提升
- **当前**：40%
- **预期**：65-75%
- **提升幅度**：25-35%

### 行为真实性增强
1. **时间随机性**：增加了更多随机延迟
2. **鼠标轨迹**：模拟真实的鼠标移动路径
3. **页面交互**：增加了滚动、焦点切换等自然行为
4. **决策犹豫**：在关键点增加思考时间
5. **探索行为**：模拟真人查看页面的习惯

## 技术特点

### 保守改动
- ✅ 保持原有代码结构不变
- ✅ 只在关键位置添加行为模拟
- ✅ 不影响主要业务逻辑
- ✅ 可以随时回滚

### 智能随机化
- 所有行为都有随机性
- 概率控制确保不会过度执行
- 时间和位置都有合理范围
- 符合真人操作习惯

### 重点优化Step 12
- 这是最容易被检测的关键节点
- 增加了3层行为模拟
- 从验证成功到点击copy按钮的完整流程优化

## 使用方法

直接运行现有命令，无需任何配置更改：
```bash
node .\run-email-verification.js
```

所有优化都会自动生效，日志中会显示真人行为模拟的执行情况。
